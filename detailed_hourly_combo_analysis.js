const fs = require('fs');
const path = require('path');

console.log('🕐 DETAILED HOURLY COMBO ANALYSIS - ALL COMBOS');
console.log('===============================================');

const CONFIG = {
  incomingDir: 'INCOMING_COMBOS',
  outDir: `detailed_hourly_analysis_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`,
  
  // Exclude analysis directories
  excludeDirs: ['analysis'],
  
  // Minimum trades per hour for reliable analysis
  minTradesForAnalysis: 50,
  
  // Selected winner for comparison
  selectedWinner: 'SL10_TP10'
};

class DetailedHourlyAnalyzer {
  constructor() {
    this.combos = [];
    this.hourlyData = new Map(); // hour -> combo -> data
    this.comboHourlyData = new Map(); // combo -> hour -> data
  }

  cleanComboName(comboName) {
    return comboName
      .replace('__PM2', '_PM2')
      .replace('__PM', '_PM')
      .replace('__ALT2', '_2')
      .replace('__ALT', '_ALT');
  }

  isPMCombo(comboName) {
    return comboName.includes('__PM2') || comboName.includes('__PM');
  }

  async analyzeHourlyPerformance() {
    console.log('📊 Starting detailed hourly analysis...');
    
    if (!fs.existsSync(CONFIG.outDir)) {
      fs.mkdirSync(CONFIG.outDir, { recursive: true });
    }

    // Get all combo directories
    const comboDirectories = fs.readdirSync(CONFIG.incomingDir)
      .filter(dir => fs.statSync(path.join(CONFIG.incomingDir, dir)).isDirectory())
      .filter(dir => !CONFIG.excludeDirs.includes(dir));

    console.log(`📁 Found ${comboDirectories.length} combo directories`);

    // Initialize hourly data structure
    for (let hour = 0; hour < 24; hour++) {
      this.hourlyData.set(hour, new Map());
    }

    // Analyze each combo
    for (const combo of comboDirectories) {
      console.log(`🔍 Analyzing ${combo}...`);
      await this.analyzeComboHourly(combo);
    }

    // Generate comprehensive hourly reports
    await this.generateHourlyPerformanceReport();
    await this.generateComboHourlyBreakdown();
    await this.generateBestComboByHour();
    await this.generateHourlyWinRateAnalysis();
    await this.generateHourlyPnLAnalysis();
    await this.generateHourlyRecommendations();
    await this.generateSelectedWinnerHourlyAnalysis();

    console.log(`✅ Detailed hourly analysis complete! Reports saved to: ${CONFIG.outDir}`);
  }

  async analyzeComboHourly(comboName) {
    const comboPath = path.join(CONFIG.incomingDir, comboName);
    const files = fs.readdirSync(comboPath).filter(f => f.endsWith('.json'));

    const cleanName = this.cleanComboName(comboName);
    const isPM = this.isPMCombo(comboName);

    // Initialize combo hourly data
    const comboHourlyStats = new Map();
    for (let hour = 0; hour < 24; hour++) {
      comboHourlyStats.set(hour, {
        trades: 0,
        wins: 0,
        losses: 0,
        totalPnL: 0,
        winRate: 0,
        avgPnL: 0,
        bestTrade: -Infinity,
        worstTrade: Infinity,
        tradeDays: new Set(),
        dailyPnLs: []
      });
    }

    // Process all files for this combo
    for (const file of files) {
      const filePath = path.join(comboPath, file);
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      const date = file.replace('.json', '');

      if (Array.isArray(data)) {
        // Track daily P&L by hour
        const dailyHourlyPnL = new Map();
        for (let h = 0; h < 24; h++) {
          dailyHourlyPnL.set(h, 0);
        }

        for (const trade of data) {
          const hour = trade.entryHourUTC || 0;
          const pnl = trade.pnl || 0;

          // Skip overlap hour for PM combos (hour 17)
          if (isPM && hour === 17) {
            continue;
          }

          const hourStats = comboHourlyStats.get(hour);
          
          hourStats.trades++;
          hourStats.totalPnL += pnl;
          hourStats.tradeDays.add(date);
          dailyHourlyPnL.set(hour, dailyHourlyPnL.get(hour) + pnl);

          if (pnl > 0) {
            hourStats.wins++;
          } else if (pnl < 0) {
            hourStats.losses++;
          }

          if (pnl > hourStats.bestTrade) {
            hourStats.bestTrade = pnl;
          }
          if (pnl < hourStats.worstTrade) {
            hourStats.worstTrade = pnl;
          }
        }

        // Store daily P&L for each hour
        for (let hour = 0; hour < 24; hour++) {
          const dayPnL = dailyHourlyPnL.get(hour);
          if (dayPnL !== 0) {
            comboHourlyStats.get(hour).dailyPnLs.push({ date, pnl: dayPnL });
          }
        }
      }
    }

    // Calculate derived metrics and store data
    for (let hour = 0; hour < 24; hour++) {
      const stats = comboHourlyStats.get(hour);
      
      if (stats.trades > 0) {
        stats.winRate = (stats.wins / stats.trades) * 100;
        stats.avgPnL = stats.totalPnL / stats.trades;
        stats.tradingDays = stats.tradeDays.size;
        stats.avgDailyPnL = stats.dailyPnLs.length > 0 ? 
          stats.dailyPnLs.reduce((sum, d) => sum + d.pnl, 0) / stats.dailyPnLs.length : 0;
        
        // Calculate daily win rate
        const profitableDays = stats.dailyPnLs.filter(d => d.pnl > 0).length;
        stats.dailyWinRate = stats.dailyPnLs.length > 0 ? (profitableDays / stats.dailyPnLs.length) * 100 : 0;
        
        // Store in global hourly data
        this.hourlyData.get(hour).set(cleanName, {
          combo: cleanName,
          isPM,
          ...stats
        });
      }
    }

    this.comboHourlyData.set(cleanName, comboHourlyStats);
  }

  async generateHourlyPerformanceReport() {
    console.log('📊 Generating hourly performance report...');

    let report = '🕐 DETAILED HOURLY PERFORMANCE ANALYSIS\n';
    report += '='.repeat(120) + '\n\n';
    report += `📊 Analysis Date: ${new Date().toISOString().slice(0, 19)}\n`;
    report += `📁 Source: ${CONFIG.incomingDir}\n`;
    report += `⚠️  PM combos exclude hour 17 UTC to prevent overlap\n`;
    report += `📏 Minimum trades for analysis: ${CONFIG.minTradesForAnalysis}\n\n`;

    // Overall hourly summary
    report += 'COMPREHENSIVE HOURLY TRADING SUMMARY:\n';
    report += '-'.repeat(140) + '\n';
    report += 'Hour | UTC Time | CST Time | Total Trades | Total P&L | Avg P&L/Trade | Active Combos | Best Combo (P&L) | Best Combo (WR)\n';
    report += '-'.repeat(140) + '\n';

    for (let hour = 0; hour < 24; hour++) {
      const hourData = this.hourlyData.get(hour);
      
      if (hourData.size > 0) {
        let totalTrades = 0;
        let totalPnL = 0;
        let bestComboPnL = { combo: '', pnl: -Infinity };
        let bestComboWR = { combo: '', wr: -1 };

        for (const [combo, data] of hourData) {
          totalTrades += data.trades;
          totalPnL += data.totalPnL;
          
          if (data.totalPnL > bestComboPnL.pnl && data.trades >= CONFIG.minTradesForAnalysis) {
            bestComboPnL = { combo, pnl: data.totalPnL };
          }
          
          if (data.winRate > bestComboWR.wr && data.trades >= CONFIG.minTradesForAnalysis) {
            bestComboWR = { combo, wr: data.winRate };
          }
        }

        const avgPnLPerTrade = totalTrades > 0 ? totalPnL / totalTrades : 0;
        const cstHour = (hour - 6 + 24) % 24;
        
        const hourStr = hour.toString().padStart(4);
        const utcTime = `${hour.toString().padStart(2, '0')}:00`.padStart(8);
        const cstTime = `${cstHour.toString().padStart(2, '0')}:00`.padStart(8);
        const trades = totalTrades.toString().padStart(12);
        const pnl = `$${totalPnL.toFixed(0)}`.padStart(9);
        const avgPnL = `$${avgPnLPerTrade.toFixed(2)}`.padStart(13);
        const activeCombos = hourData.size.toString().padStart(13);
        const bestPnL = bestComboPnL.combo.slice(0, 12).padEnd(17);
        const bestWR = bestComboWR.combo.slice(0, 12).padEnd(15);

        report += `${hourStr} | ${utcTime} | ${cstTime} | ${trades} | ${pnl} | ${avgPnL} | ${activeCombos} | ${bestPnL} | ${bestWR}\n`;
      }
    }

    report += '\n\n';

    // Best and worst hours analysis
    const hourPerformance = [];
    for (let hour = 0; hour < 24; hour++) {
      const hourData = this.hourlyData.get(hour);
      if (hourData.size > 0) {
        let totalPnL = 0;
        let totalTrades = 0;
        let totalWins = 0;
        for (const [, data] of hourData) {
          totalPnL += data.totalPnL;
          totalTrades += data.trades;
          totalWins += data.wins;
        }
        const winRate = totalTrades > 0 ? (totalWins / totalTrades) * 100 : 0;
        hourPerformance.push({ 
          hour, 
          totalPnL, 
          totalTrades, 
          avgPnL: totalPnL / totalTrades,
          winRate,
          activeCombos: hourData.size
        });
      }
    }

    hourPerformance.sort((a, b) => b.totalPnL - a.totalPnL);

    report += '🏆 TOP 10 MOST PROFITABLE HOURS:\n';
    report += '-'.repeat(80) + '\n';
    hourPerformance.slice(0, 10).forEach((h, index) => {
      const cstHour = (h.hour - 6 + 24) % 24;
      report += `${index + 1}. Hour ${h.hour} UTC (${cstHour}:00 CST): $${h.totalPnL.toFixed(0)}\n`;
      report += `   ${h.totalTrades} trades, ${h.winRate.toFixed(1)}% WR, $${h.avgPnL.toFixed(2)}/trade, ${h.activeCombos} active combos\n\n`;
    });

    // Sort by win rate
    const hoursByWinRate = [...hourPerformance].sort((a, b) => b.winRate - a.winRate);
    
    report += '🎯 TOP 10 HOURS BY WIN RATE:\n';
    report += '-'.repeat(80) + '\n';
    hoursByWinRate.slice(0, 10).forEach((h, index) => {
      const cstHour = (h.hour - 6 + 24) % 24;
      report += `${index + 1}. Hour ${h.hour} UTC (${cstHour}:00 CST): ${h.winRate.toFixed(1)}% WR\n`;
      report += `   $${h.totalPnL.toFixed(0)} total, ${h.totalTrades} trades, $${h.avgPnL.toFixed(2)}/trade\n\n`;
    });

    fs.writeFileSync(path.join(CONFIG.outDir, 'DETAILED_HOURLY_PERFORMANCE.txt'), report);
  }

  async generateBestComboByHour() {
    console.log('🏆 Generating best combo by hour analysis...');

    let report = '🏆 BEST PERFORMING COMBO BY HOUR ANALYSIS\n';
    report += '='.repeat(100) + '\n\n';

    // Best by total P&L
    report += '💰 BEST COMBO BY TOTAL P&L PER HOUR:\n';
    report += '-'.repeat(120) + '\n';
    report += 'Hour | UTC | CST | Best Combo           | Total P&L | Trades | Win Rate | Avg P&L/Trade | Daily WR | Trading Days\n';
    report += '-'.repeat(120) + '\n';

    for (let hour = 0; hour < 24; hour++) {
      const hourData = this.hourlyData.get(hour);

      if (hourData.size > 0) {
        let bestByPnL = { combo: '', data: { totalPnL: -Infinity } };

        for (const [combo, data] of hourData) {
          if (data.totalPnL > bestByPnL.data.totalPnL && data.trades >= CONFIG.minTradesForAnalysis) {
            bestByPnL = { combo, data };
          }
        }

        if (bestByPnL.data.totalPnL > -Infinity) {
          const cstHour = (hour - 6 + 24) % 24;
          const hourStr = hour.toString().padStart(4);
          const utc = hour.toString().padStart(3);
          const cst = cstHour.toString().padStart(3);
          const combo = bestByPnL.combo.padEnd(20);
          const pnl = `$${bestByPnL.data.totalPnL.toFixed(0)}`.padStart(9);
          const trades = bestByPnL.data.trades.toString().padStart(6);
          const winRate = `${bestByPnL.data.winRate.toFixed(1)}%`.padStart(8);
          const avgPnL = `$${bestByPnL.data.avgPnL.toFixed(2)}`.padStart(13);
          const dailyWR = `${bestByPnL.data.dailyWinRate.toFixed(1)}%`.padStart(8);
          const days = bestByPnL.data.tradingDays.toString().padStart(12);

          report += `${hourStr} | ${utc} | ${cst} | ${combo} | ${pnl} | ${trades} | ${winRate} | ${avgPnL} | ${dailyWR} | ${days}\n`;
        }
      }
    }

    report += '\n\n';

    // Best by win rate
    report += '🎯 BEST COMBO BY WIN RATE PER HOUR:\n';
    report += '-'.repeat(120) + '\n';
    report += 'Hour | UTC | CST | Best Combo           | Win Rate | Trades | Total P&L | Avg P&L/Trade | Daily WR | Trading Days\n';
    report += '-'.repeat(120) + '\n';

    for (let hour = 0; hour < 24; hour++) {
      const hourData = this.hourlyData.get(hour);

      if (hourData.size > 0) {
        let bestByWR = { combo: '', data: { winRate: -1 } };

        for (const [combo, data] of hourData) {
          if (data.winRate > bestByWR.data.winRate && data.trades >= CONFIG.minTradesForAnalysis) {
            bestByWR = { combo, data };
          }
        }

        if (bestByWR.data.winRate > -1) {
          const cstHour = (hour - 6 + 24) % 24;
          const hourStr = hour.toString().padStart(4);
          const utc = hour.toString().padStart(3);
          const cst = cstHour.toString().padStart(3);
          const combo = bestByWR.combo.padEnd(20);
          const winRate = `${bestByWR.data.winRate.toFixed(1)}%`.padStart(8);
          const trades = bestByWR.data.trades.toString().padStart(6);
          const pnl = `$${bestByWR.data.totalPnL.toFixed(0)}`.padStart(9);
          const avgPnL = `$${bestByWR.data.avgPnL.toFixed(2)}`.padStart(13);
          const dailyWR = `${bestByWR.data.dailyWinRate.toFixed(1)}%`.padStart(8);
          const days = bestByWR.data.tradingDays.toString().padStart(12);

          report += `${hourStr} | ${utc} | ${cst} | ${combo} | ${winRate} | ${trades} | ${pnl} | ${avgPnL} | ${dailyWR} | ${days}\n`;
        }
      }
    }

    fs.writeFileSync(path.join(CONFIG.outDir, 'BEST_COMBO_BY_HOUR.txt'), report);
  }

  async generateComboHourlyBreakdown() {
    console.log('📋 Generating combo hourly breakdown...');

    let report = '📋 INDIVIDUAL COMBO HOURLY BREAKDOWN\n';
    report += '='.repeat(80) + '\n\n';

    // Get top 20 combos by total P&L for detailed analysis
    const allCombos = [];
    for (const [combo, hourlyStats] of this.comboHourlyData) {
      let totalPnL = 0;
      let totalTrades = 0;
      for (const [, stats] of hourlyStats) {
        totalPnL += stats.totalPnL;
        totalTrades += stats.trades;
      }
      allCombos.push({ combo, totalPnL, totalTrades });
    }

    allCombos.sort((a, b) => b.totalPnL - a.totalPnL);
    const topCombos = allCombos.slice(0, 20);

    for (const { combo } of topCombos) {
      const hourlyStats = this.comboHourlyData.get(combo);

      report += `🔍 ${combo} HOURLY BREAKDOWN:\n`;
      report += '-'.repeat(100) + '\n';
      report += 'Hour | UTC | CST | Trades | Total P&L | Win Rate | Avg P&L/Trade | Daily WR | Best Trade | Worst Trade\n';
      report += '-'.repeat(100) + '\n';

      for (let hour = 0; hour < 24; hour++) {
        const stats = hourlyStats.get(hour);

        if (stats.trades > 0) {
          const cstHour = (hour - 6 + 24) % 24;
          const hourStr = hour.toString().padStart(4);
          const utc = hour.toString().padStart(3);
          const cst = cstHour.toString().padStart(3);
          const trades = stats.trades.toString().padStart(6);
          const pnl = `$${stats.totalPnL.toFixed(0)}`.padStart(9);
          const winRate = `${stats.winRate.toFixed(1)}%`.padStart(8);
          const avgPnL = `$${stats.avgPnL.toFixed(2)}`.padStart(13);
          const dailyWR = `${stats.dailyWinRate.toFixed(1)}%`.padStart(8);
          const best = `$${stats.bestTrade.toFixed(0)}`.padStart(10);
          const worst = `$${stats.worstTrade.toFixed(0)}`.padStart(11);

          report += `${hourStr} | ${utc} | ${cst} | ${trades} | ${pnl} | ${winRate} | ${avgPnL} | ${dailyWR} | ${best} | ${worst}\n`;
        }
      }

      report += '\n';
    }

    fs.writeFileSync(path.join(CONFIG.outDir, 'COMBO_HOURLY_BREAKDOWN.txt'), report);
  }

  async generateSelectedWinnerHourlyAnalysis() {
    console.log('⭐ Generating selected winner hourly analysis...');

    const selectedCombo = this.comboHourlyData.get(CONFIG.selectedWinner);
    if (!selectedCombo) {
      console.log(`⚠️  Selected winner ${CONFIG.selectedWinner} not found in data`);
      return;
    }

    let report = `⭐ ${CONFIG.selectedWinner} DETAILED HOURLY ANALYSIS\n`;
    report += '='.repeat(80) + '\n\n';
    report += `Selected Strategy: ${CONFIG.selectedWinner}\n`;
    report += `Analysis Focus: Hour-by-hour performance breakdown\n\n`;

    report += 'HOURLY PERFORMANCE BREAKDOWN:\n';
    report += '-'.repeat(120) + '\n';
    report += 'Hour | UTC | CST | Trades | Total P&L | Win Rate | Avg P&L/Trade | Daily WR | Trading Days | Avg Daily P&L\n';
    report += '-'.repeat(120) + '\n';

    let totalTrades = 0;
    let totalPnL = 0;
    let bestHour = { hour: -1, pnl: -Infinity };
    let worstHour = { hour: -1, pnl: Infinity };

    for (let hour = 0; hour < 24; hour++) {
      const stats = selectedCombo.get(hour);

      if (stats.trades > 0) {
        totalTrades += stats.trades;
        totalPnL += stats.totalPnL;

        if (stats.totalPnL > bestHour.pnl) {
          bestHour = { hour, pnl: stats.totalPnL };
        }
        if (stats.totalPnL < worstHour.pnl) {
          worstHour = { hour, pnl: stats.totalPnL };
        }

        const cstHour = (hour - 6 + 24) % 24;
        const hourStr = hour.toString().padStart(4);
        const utc = hour.toString().padStart(3);
        const cst = cstHour.toString().padStart(3);
        const trades = stats.trades.toString().padStart(6);
        const pnl = `$${stats.totalPnL.toFixed(0)}`.padStart(9);
        const winRate = `${stats.winRate.toFixed(1)}%`.padStart(8);
        const avgPnL = `$${stats.avgPnL.toFixed(2)}`.padStart(13);
        const dailyWR = `${stats.dailyWinRate.toFixed(1)}%`.padStart(8);
        const days = stats.tradingDays.toString().padStart(12);
        const avgDaily = `$${stats.avgDailyPnL.toFixed(0)}`.padStart(13);

        report += `${hourStr} | ${utc} | ${cst} | ${trades} | ${pnl} | ${winRate} | ${avgPnL} | ${dailyWR} | ${days} | ${avgDaily}\n`;
      }
    }

    report += '\n\n';

    report += `📊 ${CONFIG.selectedWinner} SUMMARY:\n`;
    report += '-'.repeat(50) + '\n';
    report += `Total Trades: ${totalTrades}\n`;
    report += `Total P&L: $${totalPnL.toFixed(0)}\n`;
    report += `Overall Win Rate: ${totalTrades > 0 ? ((totalPnL > 0 ? 1 : 0) * 100).toFixed(1) : 0}%\n`;
    report += `Best Hour: ${bestHour.hour} UTC (${((bestHour.hour - 6 + 24) % 24)}:00 CST) - $${bestHour.pnl.toFixed(0)}\n`;
    report += `Worst Hour: ${worstHour.hour} UTC (${((worstHour.hour - 6 + 24) % 24)}:00 CST) - $${worstHour.pnl.toFixed(0)}\n\n`;

    report += `💡 INSIGHTS FOR ${CONFIG.selectedWinner}:\n`;
    report += '-'.repeat(50) + '\n';

    // Find best and worst performing hours
    const hourPerformance = [];
    for (let hour = 0; hour < 24; hour++) {
      const stats = selectedCombo.get(hour);
      if (stats.trades > 0) {
        hourPerformance.push({ hour, ...stats });
      }
    }

    hourPerformance.sort((a, b) => b.totalPnL - a.totalPnL);

    report += `• Best 3 hours: `;
    hourPerformance.slice(0, 3).forEach((h, i) => {
      const cst = (h.hour - 6 + 24) % 24;
      report += `${h.hour}UTC(${cst}CST)`;
      if (i < 2) report += ', ';
    });
    report += '\n';

    report += `• Worst 3 hours: `;
    hourPerformance.slice(-3).reverse().forEach((h, i) => {
      const cst = (h.hour - 6 + 24) % 24;
      report += `${h.hour}UTC(${cst}CST)`;
      if (i < 2) report += ', ';
    });
    report += '\n';

    const avgWinRate = hourPerformance.reduce((sum, h) => sum + h.winRate, 0) / hourPerformance.length;
    const highWRHours = hourPerformance.filter(h => h.winRate > avgWinRate);

    report += `• Hours above average win rate (${avgWinRate.toFixed(1)}%): ${highWRHours.length}/${hourPerformance.length}\n`;
    report += `• Most consistent hour: ${hourPerformance.reduce((best, h) => h.winRate > best.winRate ? h : best).hour} UTC (${((hourPerformance.reduce((best, h) => h.winRate > best.winRate ? h : best).hour - 6 + 24) % 24)}:00 CST)\n`;

    fs.writeFileSync(path.join(CONFIG.outDir, `${CONFIG.selectedWinner}_HOURLY_ANALYSIS.txt`), report);
  }

  async generateHourlyWinRateAnalysis() {
    console.log('🎯 Generating hourly win rate analysis...');

    let report = '🎯 HOURLY WIN RATE ANALYSIS\n';
    report += '='.repeat(80) + '\n\n';

    report += 'WIN RATE ANALYSIS BY HOUR:\n';
    report += '-'.repeat(100) + '\n';
    report += 'Hour | UTC | CST | Avg WR | Best WR | Worst WR | Best Combo (WR)     | Worst Combo (WR)    | Active Combos\n';
    report += '-'.repeat(100) + '\n';

    for (let hour = 0; hour < 24; hour++) {
      const hourData = this.hourlyData.get(hour);

      if (hourData.size > 0) {
        let totalWR = 0;
        let count = 0;
        let bestWR = { combo: '', wr: -1 };
        let worstWR = { combo: '', wr: 101 };

        for (const [combo, data] of hourData) {
          if (data.trades >= CONFIG.minTradesForAnalysis) {
            totalWR += data.winRate;
            count++;

            if (data.winRate > bestWR.wr) {
              bestWR = { combo, wr: data.winRate };
            }
            if (data.winRate < worstWR.wr) {
              worstWR = { combo, wr: data.winRate };
            }
          }
        }

        if (count > 0) {
          const avgWR = totalWR / count;
          const cstHour = (hour - 6 + 24) % 24;

          const hourStr = hour.toString().padStart(4);
          const utc = hour.toString().padStart(3);
          const cst = cstHour.toString().padStart(3);
          const avg = `${avgWR.toFixed(1)}%`.padStart(6);
          const best = `${bestWR.wr.toFixed(1)}%`.padStart(7);
          const worst = `${worstWR.wr.toFixed(1)}%`.padStart(8);
          const bestCombo = bestWR.combo.slice(0, 18).padEnd(20);
          const worstCombo = worstWR.combo.slice(0, 18).padEnd(20);
          const active = count.toString().padStart(13);

          report += `${hourStr} | ${utc} | ${cst} | ${avg} | ${best} | ${worst} | ${bestCombo} | ${worstCombo} | ${active}\n`;
        }
      }
    }

    fs.writeFileSync(path.join(CONFIG.outDir, 'HOURLY_WIN_RATE_ANALYSIS.txt'), report);
  }

  async generateHourlyPnLAnalysis() {
    console.log('💰 Generating hourly P&L analysis...');

    let report = '💰 HOURLY P&L ANALYSIS\n';
    report += '='.repeat(80) + '\n\n';

    report += 'P&L ANALYSIS BY HOUR:\n';
    report += '-'.repeat(120) + '\n';
    report += 'Hour | UTC | CST | Total P&L | Avg P&L/Trade | Best Combo (P&L)    | Best P&L  | Worst Combo (P&L)   | Worst P&L\n';
    report += '-'.repeat(120) + '\n';

    for (let hour = 0; hour < 24; hour++) {
      const hourData = this.hourlyData.get(hour);

      if (hourData.size > 0) {
        let totalPnL = 0;
        let totalTrades = 0;
        let bestPnL = { combo: '', pnl: -Infinity };
        let worstPnL = { combo: '', pnl: Infinity };

        for (const [combo, data] of hourData) {
          if (data.trades >= CONFIG.minTradesForAnalysis) {
            totalPnL += data.totalPnL;
            totalTrades += data.trades;

            if (data.totalPnL > bestPnL.pnl) {
              bestPnL = { combo, pnl: data.totalPnL };
            }
            if (data.totalPnL < worstPnL.pnl) {
              worstPnL = { combo, pnl: data.totalPnL };
            }
          }
        }

        if (totalTrades > 0) {
          const avgPnLPerTrade = totalPnL / totalTrades;
          const cstHour = (hour - 6 + 24) % 24;

          const hourStr = hour.toString().padStart(4);
          const utc = hour.toString().padStart(3);
          const cst = cstHour.toString().padStart(3);
          const total = `$${totalPnL.toFixed(0)}`.padStart(9);
          const avg = `$${avgPnLPerTrade.toFixed(2)}`.padStart(13);
          const bestCombo = bestPnL.combo.slice(0, 18).padEnd(20);
          const bestVal = `$${bestPnL.pnl.toFixed(0)}`.padStart(9);
          const worstCombo = worstPnL.combo.slice(0, 18).padEnd(20);
          const worstVal = `$${worstPnL.pnl.toFixed(0)}`.padStart(9);

          report += `${hourStr} | ${utc} | ${cst} | ${total} | ${avg} | ${bestCombo} | ${bestVal} | ${worstCombo} | ${worstVal}\n`;
        }
      }
    }

    fs.writeFileSync(path.join(CONFIG.outDir, 'HOURLY_PNL_ANALYSIS.txt'), report);
  }

  async generateHourlyRecommendations() {
    console.log('💡 Generating hourly recommendations...');

    let report = '💡 HOURLY TRADING RECOMMENDATIONS\n';
    report += '='.repeat(80) + '\n\n';

    // Analyze each hour for recommendations
    const hourAnalysis = [];

    for (let hour = 0; hour < 24; hour++) {
      const hourData = this.hourlyData.get(hour);

      if (hourData.size > 0) {
        let totalPnL = 0;
        let totalTrades = 0;
        let totalWins = 0;
        let bestCombo = { combo: '', pnl: -Infinity, wr: 0 };
        let combosWithGoodData = 0;

        for (const [combo, data] of hourData) {
          if (data.trades >= CONFIG.minTradesForAnalysis) {
            combosWithGoodData++;
            totalPnL += data.totalPnL;
            totalTrades += data.trades;
            totalWins += data.wins;

            if (data.totalPnL > bestCombo.pnl) {
              bestCombo = { combo, pnl: data.totalPnL, wr: data.winRate };
            }
          }
        }

        if (combosWithGoodData > 0) {
          const avgWinRate = (totalWins / totalTrades) * 100;
          const avgPnLPerTrade = totalPnL / totalTrades;
          const cstHour = (hour - 6 + 24) % 24;

          let recommendation = 'TRADE';
          let reason = '';

          if (avgWinRate < 45) {
            recommendation = 'CAUTION';
            reason = 'Low win rate';
          } else if (avgPnLPerTrade < 5) {
            recommendation = 'CAUTION';
            reason = 'Low avg P&L per trade';
          } else if (totalPnL < 0) {
            recommendation = 'AVOID';
            reason = 'Negative total P&L';
          } else if (avgWinRate > 65 && avgPnLPerTrade > 20) {
            recommendation = 'OPTIMAL';
            reason = 'High win rate and P&L';
          }

          hourAnalysis.push({
            hour,
            cstHour,
            totalPnL,
            avgWinRate,
            avgPnLPerTrade,
            bestCombo: bestCombo.combo,
            bestComboPnL: bestCombo.pnl,
            bestComboWR: bestCombo.wr,
            recommendation,
            reason,
            activeCombos: combosWithGoodData
          });
        }
      }
    }

    // Sort by recommendation priority
    const recommendationOrder = { 'OPTIMAL': 1, 'TRADE': 2, 'CAUTION': 3, 'AVOID': 4 };
    hourAnalysis.sort((a, b) => {
      const orderDiff = recommendationOrder[a.recommendation] - recommendationOrder[b.recommendation];
      if (orderDiff !== 0) return orderDiff;
      return b.totalPnL - a.totalPnL; // Secondary sort by P&L
    });

    report += 'HOURLY TRADING RECOMMENDATIONS:\n';
    report += '-'.repeat(120) + '\n';
    report += 'Hour | UTC | CST | Recommendation | Total P&L | Avg WR | Avg P&L/Trade | Best Combo           | Reason\n';
    report += '-'.repeat(120) + '\n';

    hourAnalysis.forEach(h => {
      const hourStr = h.hour.toString().padStart(4);
      const utc = h.hour.toString().padStart(3);
      const cst = h.cstHour.toString().padStart(3);
      const rec = h.recommendation.padEnd(14);
      const pnl = `$${h.totalPnL.toFixed(0)}`.padStart(9);
      const wr = `${h.avgWinRate.toFixed(1)}%`.padStart(6);
      const avg = `$${h.avgPnLPerTrade.toFixed(2)}`.padStart(13);
      const combo = h.bestCombo.slice(0, 18).padEnd(20);
      const reason = h.reason;

      report += `${hourStr} | ${utc} | ${cst} | ${rec} | ${pnl} | ${wr} | ${avg} | ${combo} | ${reason}\n`;
    });

    report += '\n\n';

    // Summary recommendations
    const optimal = hourAnalysis.filter(h => h.recommendation === 'OPTIMAL');
    const trade = hourAnalysis.filter(h => h.recommendation === 'TRADE');
    const caution = hourAnalysis.filter(h => h.recommendation === 'CAUTION');
    const avoid = hourAnalysis.filter(h => h.recommendation === 'AVOID');

    report += '📊 SUMMARY RECOMMENDATIONS:\n';
    report += '-'.repeat(50) + '\n';
    report += `🟢 OPTIMAL Hours (${optimal.length}): `;
    optimal.forEach((h, i) => {
      report += `${h.hour}UTC(${h.cstHour}CST)`;
      if (i < optimal.length - 1) report += ', ';
    });
    report += '\n';

    report += `🔵 TRADE Hours (${trade.length}): `;
    trade.forEach((h, i) => {
      report += `${h.hour}UTC(${h.cstHour}CST)`;
      if (i < trade.length - 1) report += ', ';
    });
    report += '\n';

    report += `🟡 CAUTION Hours (${caution.length}): `;
    caution.forEach((h, i) => {
      report += `${h.hour}UTC(${h.cstHour}CST)`;
      if (i < caution.length - 1) report += ', ';
    });
    report += '\n';

    report += `🔴 AVOID Hours (${avoid.length}): `;
    avoid.forEach((h, i) => {
      report += `${h.hour}UTC(${h.cstHour}CST)`;
      if (i < avoid.length - 1) report += ', ';
    });
    report += '\n\n';

    report += '💡 KEY INSIGHTS:\n';
    report += '-'.repeat(30) + '\n';
    report += `• Best overall hour: ${hourAnalysis[0].hour} UTC (${hourAnalysis[0].cstHour}:00 CST)\n`;
    report += `• Most profitable combo: ${hourAnalysis[0].bestCombo}\n`;
    report += `• Hours to avoid: ${avoid.length > 0 ? avoid.map(h => `${h.hour}UTC`).join(', ') : 'None'}\n`;
    report += `• Optimal trading window: ${optimal.length > 0 ? `${Math.min(...optimal.map(h => h.hour))}-${Math.max(...optimal.map(h => h.hour))} UTC` : 'See TRADE hours'}\n`;

    fs.writeFileSync(path.join(CONFIG.outDir, 'HOURLY_RECOMMENDATIONS.txt'), report);
  }
}

// Run the detailed hourly analysis
const analyzer = new DetailedHourlyAnalyzer();
analyzer.analyzeHourlyPerformance().catch(console.error);
